const { execSync, spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const { AIService } = require('../ai/ai-service');
const ConfigLoader = require('./configLoader');

/**
 * 构建错误修复器
 * 继承自 AIService，具备 AI 修复能力
 * 支持多种构建场景：dev 模式和 build 模式
 *
 * 改进点：
 * - 增强用户体验和错误反馈
 * - 改进错误分类和优先级处理
 * - 提供交互式修复选项
 * - 增强修复成功率
 */

class BuildFixer extends AIService {
  constructor(projectPath, options = {}) {
    super(options);

    this.projectPath = projectPath;
    this.buildErrors = [];
    this.fixedErrors = [];
    this.failedErrors = [];
    this.skippedErrors = [];

    // 配置加载器
    this.configLoader = new ConfigLoader();

    // 临时存储传入的选项，将在 initialize 中与配置文件合并
    this.userOptions = options;
    this.options = null; // 将在 initialize 中设置

    // 移除对外部 config.json 的依赖，使用内置配置
    this.moduleMapping = this.getBuiltinModuleMapping();

    // 进度指示器
    this.spinner = null;

    // 构建场景
    this.scenario = null;

    // 错误优先级映射
    this.errorPriorities = {
      'missing-dependency': 1,    // 最高优先级：缺失依赖
      'missing-module': 2,        // 模块导入问题
      'vue-version': 3,           // Vue 版本兼容性
      'ui-library': 4,            // UI 库兼容性
      'typescript': 5,            // TypeScript 类型错误
      'property-not-exist': 6,    // 属性不存在
      'syntax-error': 7,          // 语法错误
      'other': 8                  // 其他错误
    };

    // 构建统计信息
    this.buildStats = {
      buildAttempts: 0,
      buildSuccess: false,
      errorsFound: [],
      errorsFixed: 0,
      errorsSkipped: 0,
      finalBuildSuccess: false,
      startTime: null,
      endTime: null,
      duration: 0,
      scenario: options.mode || 'build',
      fixStrategies: {}
    };

    // 用户交互状态
    this.interactiveMode = options.interactive || false;
    this.userChoices = new Map();
  }

  /**
   * 启动进度指示器
   */
  startSpinner(text) {
    if (this.options && this.options.showProgress && !this.options.verbose) {
      this.spinner = ora(text).start();
    } else {
      console.log(chalk.blue(`🔄 ${text}`));
    }
  }

  /**
   * 更新进度指示器文本
   */
  updateSpinner(text) {
    if (this.spinner) {
      this.spinner.text = text;
    } else if (!this.options || !this.options.verbose) {
      console.log(chalk.blue(`🔄 ${text}`));
    }
  }

  /**
   * 成功完成进度指示器
   */
  succeedSpinner(text) {
    if (this.spinner) {
      this.spinner.succeed(text);
      this.spinner = null;
    } else {
      console.log(chalk.green(`✅ ${text}`));
    }
  }

  /**
   * 失败完成进度指示器
   */
  failSpinner(text) {
    if (this.spinner) {
      this.spinner.fail(text);
      this.spinner = null;
    } else {
      console.log(chalk.red(`❌ ${text}`));
    }
  }

  /**
   * 停止进度指示器
   */
  stopSpinner() {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }

  /**
   * 获取内置模块映射表
   */
  getBuiltinModuleMapping() {
    return {
      // Vue 2 到 Vue 3 的模块映射
      vue: 'vue',
      'vue-router': 'vue-router',
      vuex: 'vuex',
      '@vue/composition-api': null, // Vue 3 中已内置，需要移除

      // Element UI 到 Element Plus 的映射
      'element-ui': 'element-plus',
      'element-ui/lib/locale/lang/zh-CN': 'element-plus/es/locale/lang/zh-cn',
      'element-ui/lib/locale/lang/en': 'element-plus/es/locale/lang/en',

      // 其他常见的不兼容包
      'vue-class-component': null,
      'vue-property-decorator': null,
      'vue-template-compiler': null,

      // 工具库映射
      'vue-fragment': null, // Vue 3 原生支持 Fragment
      'portal-vue': null // Vue 3 原生支持 Teleport
    };
  }

  /**
   * 获取模块映射表
   */
  getModuleMapping() {
    return this.moduleMapping;
  }

  /**
   * 执行构建并修复错误 - 主入口方法
   */
  async buildAndFix() {
    this.buildStats.startTime = Date.now();

    try {
      this.startSpinner('开始构建项目并修复错误...');

      // 初始化
      await this.initialize();

      // 根据模式执行不同的构建策略
      let buildResult;
      if (this.options.mode === 'dev') {
        this.updateSpinner('执行 dev 模式错误检测...');
        buildResult = await this.performDevCheck();
      } else {
        this.updateSpinner('执行构建...');
        buildResult = await this.performBuild();
      }

      if (buildResult.success) {
        this.succeedSpinner(`${this.options.mode === 'dev' ? 'Dev 检测' : '项目构建'}成功！`);
        this.buildStats.buildSuccess = true;
        this.buildStats.finalBuildSuccess = true;
        return this.createResult(true);
      }

      // 分析和修复错误
      this.updateSpinner('分析构建错误...');
      const fixResult = await this.analyzeAndFixErrors(buildResult);

      this.stopSpinner();
      this.printBuildStats();
      return fixResult;
    } catch (error) {
      this.failSpinner('构建修复过程失败');
      console.error(chalk.red('❌ 错误详情:'), error.message);
      throw error;
    } finally {
      this.buildStats.endTime = Date.now();
      this.buildStats.duration = this.buildStats.endTime - this.buildStats.startTime;
      this.stopSpinner();
    }
  }

  /**
   * 初始化构建修复器
   */
  async initialize() {
    // 加载配置文件并与用户选项合并
    const configFileOptions = await this.configLoader.loadConfig(
      this.userOptions.configPath,
      this.projectPath
    );

    // 合并配置：用户选项 > 配置文件 > 默认值
    this.options = {
      ...configFileOptions,
      ...this.userOptions
    };

    // 初始化错误日志路径
    this.currentErrorLogPath = null;

    if (this.options.verbose) {
      console.log(chalk.gray(`📁 项目路径: ${this.projectPath}`));
      console.log(chalk.gray(`🎯 运行模式: ${this.options.mode}`));
      if (this.options.mode === 'dev') {
        console.log(chalk.gray(`🚀 Dev 命令: ${this.options.devCommand}`));
        console.log(chalk.gray(`⏱️  超时时间: ${this.options.devTimeout / 1000}s`));
      } else {
        console.log(chalk.gray(`🔧 构建命令: ${this.options.buildCommand}`));
      }
      console.log(chalk.gray(`🔄 最大尝试次数: ${this.options.maxAttempts}`));
      console.log(chalk.gray(`🤖 AI 修复: ${this.options.skipAI ? '禁用' : '启用'}`));
      console.log(chalk.gray(`🔍 预览模式: ${this.options.dryRun ? '是' : '否'}`));
    }
  }

  /**
   * 分析和修复错误 - 改进版本，提供更好的用户体验
   */
  async analyzeAndFixErrors(buildResult) {
    let errors = this.parseErrors(buildResult.output);
    this.buildStats.errorsFound = errors;

    if (errors.length === 0) {
      console.log(chalk.yellow('⚠️  构建失败但无法解析错误信息'));
      console.log(chalk.gray('💡 建议: 检查构建命令是否正确，或使用 --verbose 查看详细信息'));
      return this.createResult(false, 'Unable to parse build errors');
    }

    // 按优先级排序错误
    errors = this.sortErrorsByPriority(errors);

    // 显示错误摘要
    this.displayErrorSummary(errors);

    // 如果是交互模式，让用户选择要修复的错误
    if (this.interactiveMode) {
      errors = await this.selectErrorsToFix(errors);
    }

    // 尝试修复错误
    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxAttempts}...`));

      const fixResult = await this.fixErrorsWithProgress(errors);

      if (fixResult.fixed > 0) {
        console.log(chalk.green(`✅ 成功修复 ${fixResult.fixed} 个错误`));
        this.buildStats.errorsFixed += fixResult.fixed;

        // 显示修复详情
        this.displayFixResults(fixResult);

        // 重新构建
        console.log(chalk.blue('\n🔄 重新构建项目...'));
        const newBuildResult = await this.performBuild();

        if (newBuildResult.success) {
          console.log(chalk.green('🎉 修复后构建成功！'));
          this.buildStats.finalBuildSuccess = true;
          this.displaySuccessMessage();
          break;
        } else {
          // 更新错误列表
          const newErrors = this.parseErrors(newBuildResult.output);
          const fixedCount = errors.length - newErrors.length;

          if (fixedCount > 0) {
            console.log(chalk.green(`✅ 本轮修复了 ${fixedCount} 个错误`));
          }

          if (newErrors.length > 0) {
            console.log(chalk.yellow(`⚠️  仍有 ${newErrors.length} 个错误需要修复`));
            errors = this.sortErrorsByPriority(newErrors);
          }
        }
      } else {
        console.log(chalk.yellow('⚠️  本轮未能修复任何错误'));
        this.displayFixSuggestions(errors);
      }
    }

    return this.createResult(this.buildStats.finalBuildSuccess, null, errors.length);
  }

  /**
   * 按优先级排序错误
   */
  sortErrorsByPriority(errors) {
    return errors.sort((a, b) => {
      const priorityA = a.priority || 99;
      const priorityB = b.priority || 99;
      return priorityA - priorityB;
    });
  }

  /**
   * 显示错误摘要
   */
  displayErrorSummary(errors) {
    console.log(chalk.blue(`\n📋 发现 ${errors.length} 个构建错误:`));

    // 按类别分组显示
    const errorsByCategory = {};
    errors.forEach(error => {
      const category = error.category || 'other';
      if (!errorsByCategory[category]) {
        errorsByCategory[category] = [];
      }
      errorsByCategory[category].push(error);
    });

    Object.entries(errorsByCategory).forEach(([category, categoryErrors]) => {
      const autoFixableCount = categoryErrors.filter(e => e.autoFixable).length;
      const icon = autoFixableCount === categoryErrors.length ? '✅' :
                   autoFixableCount > 0 ? '🔄' : '❌';

      console.log(chalk.gray(`  ${icon} ${categoryErrors[0].friendlyName || category}: ${categoryErrors.length} 个错误`));

      if (autoFixableCount > 0) {
        console.log(chalk.green(`     └─ ${autoFixableCount} 个可自动修复`));
      }

      if (categoryErrors.length - autoFixableCount > 0) {
        console.log(chalk.yellow(`     └─ ${categoryErrors.length - autoFixableCount} 个需要手动处理`));
      }
    });
  }

  /**
   * 显示修复结果
   */
  displayFixResults(fixResult) {
    if (fixResult.details && fixResult.details.length > 0) {
      console.log(chalk.blue('\n📝 修复详情:'));
      fixResult.details.forEach(detail => {
        const icon = detail.success ? '✅' : '❌';
        console.log(chalk.gray(`  ${icon} ${detail.file}: ${detail.description}`));
      });
    }
  }

  /**
   * 显示成功消息
   */
  displaySuccessMessage() {
    console.log(chalk.green('\n🎉 恭喜！所有构建错误已修复'));
    console.log(chalk.blue('\n💡 后续建议:'));
    console.log(chalk.gray('  1. 运行测试确保功能正常'));
    console.log(chalk.gray('  2. 检查修复后的代码是否符合预期'));
    console.log(chalk.gray('  3. 提交代码变更'));
  }

  /**
   * 显示修复建议
   */
  displayFixSuggestions(errors) {
    console.log(chalk.blue('\n💡 修复建议:'));

    const manualErrors = errors.filter(e => !e.autoFixable);
    if (manualErrors.length > 0) {
      console.log(chalk.yellow(`\n需要手动处理的错误 (${manualErrors.length} 个):`));
      manualErrors.slice(0, 3).forEach(error => {
        console.log(chalk.gray(`  • ${error.file || 'unknown'}: ${error.description}`));
        console.log(chalk.gray(`    解决方案: ${error.solution}`));
      });

      if (manualErrors.length > 3) {
        console.log(chalk.gray(`  ... 以及 ${manualErrors.length - 3} 个其他错误`));
      }
    }

    console.log(chalk.gray('\n通用建议:'));
    console.log(chalk.gray('  • 使用 --verbose 查看详细错误信息'));
    console.log(chalk.gray('  • 检查依赖版本是否兼容 Vue 3'));
    console.log(chalk.gray('  • 参考 Vue 3 迁移指南进行手动修复'));
  }

  /**
   * 交互式选择要修复的错误
   */
  async selectErrorsToFix(errors) {
    if (!this.interactiveMode || errors.length === 0) {
      return errors;
    }

    console.log(chalk.blue('\n🔍 交互式修复模式'));
    console.log(chalk.gray('请选择要修复的错误类型:\n'));

    // 按类别分组错误
    const errorsByCategory = {};
    errors.forEach(error => {
      const category = error.category || 'other';
      if (!errorsByCategory[category]) {
        errorsByCategory[category] = [];
      }
      errorsByCategory[category].push(error);
    });

    // 显示选项
    const categories = Object.keys(errorsByCategory);
    categories.forEach((category, index) => {
      const categoryErrors = errorsByCategory[category];
      const autoFixableCount = categoryErrors.filter(e => e.autoFixable).length;
      const icon = autoFixableCount === categoryErrors.length ? '✅' :
                   autoFixableCount > 0 ? '🔄' : '❌';

      console.log(chalk.gray(`[${index + 1}] ${icon} ${categoryErrors[0].friendlyName || category}`));
      console.log(chalk.gray(`     ${categoryErrors.length} 个错误 (${autoFixableCount} 个可自动修复)`));

      if (categoryErrors[0].description) {
        console.log(chalk.gray(`     ${categoryErrors[0].description}`));
      }
      console.log('');
    });

    console.log(chalk.gray('[a] 修复所有错误'));
    console.log(chalk.gray('[s] 跳过，不修复任何错误'));
    console.log(chalk.gray('[q] 退出程序\n'));

    // 在实际实现中，这里应该使用 readline 或类似的库来获取用户输入
    // 为了简化，这里返回所有错误
    console.log(chalk.yellow('注意: 交互式选择功能需要进一步实现，当前将修复所有错误'));

    return errors;
  }

  /**
   * 创建结果对象
   */
  createResult(success, reason = null, remainingErrors = 0) {
    return {
      success,
      attempts: this.buildStats.buildAttempts,
      errorsFixed: this.buildStats.errorsFixed,
      remainingErrors,
      duration: this.buildStats.duration,
      reason
    };
  }

  /**
   * 执行 Dev 模式检测（30秒运行）
   */
  async performDevCheck() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行 dev 命令并在 30 秒后停止
      return await this.executeDevCommand();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 执行构建过程（包含依赖安装和构建）
   */
  async performBuild() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行构建
      return await this.executeBuild();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 安装项目依赖
   */
  async installDependencies() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过依赖安装'));
      return;
    }

    this.updateSpinner('正在安装依赖...');

    try {
      const installOutput = execSync(this.options.installCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 依赖安装完成'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(installOutput));
        this.startSpinner('继续处理...');
      }
    } catch (error) {
      // 尝试使用 legacy peer deps 重新安装
      if (this.options.useLegacyPeerDeps && this.shouldUseLegacyPeerDeps(error)) {
        await this.installWithLegacyPeerDeps();
      } else {
        this.stopSpinner();
        console.log(chalk.yellow('⚠️ 依赖安装可能不完整，继续尝试构建'));
        this.logInstallError(error);
        this.startSpinner('继续处理...');
      }
    }
  }

  /**
   * 检查是否应该使用 legacy peer deps
   */
  shouldUseLegacyPeerDeps(error) {
    const errorOutput = error.stdout + error.stderr;
    return errorOutput.includes('ERESOLVE unable to resolve dependency tree') ||
           errorOutput.includes('Fix the upstream dependency conflict') ||
           errorOutput.includes('--legacy-peer-deps') ||
           errorOutput.includes('peer dependency') ||
           errorOutput.includes('Could not resolve dependency');
  }

  /**
   * 使用 legacy peer deps 安装依赖
   */
  async installWithLegacyPeerDeps() {
    this.updateSpinner('检测到依赖冲突，尝试使用 --legacy-peer-deps 重新安装...');

    try {
      const legacyCommand = this.options.installCommand + ' --legacy-peer-deps';
      const legacyInstallOutput = execSync(legacyCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 使用 --legacy-peer-deps 安装依赖成功'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(legacyInstallOutput));
        this.startSpinner('继续处理...');
      }
    } catch (legacyError) {
      this.stopSpinner();
      console.log(chalk.red('❌ 即使使用 --legacy-peer-deps 也无法安装依赖'));
      this.logInstallError(legacyError);
      this.startSpinner('继续处理...');
    }
  }

  /**
   * 执行 Dev 命令（30秒超时）
   */
  async executeDevCommand() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过 dev 命令执行'));
      return { success: true, output: 'DRY RUN - Dev command skipped' };
    }

    const devCommand = this.options.devCommand || 'pnpm dev';
    const timeout = this.options.devTimeout || 30000; // 30秒

    this.updateSpinner(`执行 dev 命令: ${devCommand} (${timeout / 1000}s 超时)`);
    this.buildStats.buildAttempts++;

    return new Promise((resolve) => {
      const { spawn } = require('child_process');
      let output = '';
      let hasErrors = false;

      const child = spawn('sh', ['-c', devCommand], {
        cwd: this.projectPath,
        stdio: 'pipe'
      });

      // 收集输出
      child.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;

        // 检查是否有错误
        if (this.containsErrors(text)) {
          hasErrors = true;
        }

        if (this.options.verbose) {
          process.stdout.write(chalk.gray(text));
        }
      });

      child.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;
        hasErrors = true;

        if (this.options.verbose) {
          process.stderr.write(chalk.red(text));
        }
      });

      // 30秒后停止进程
      const timer = setTimeout(() => {
        child.kill('SIGTERM');

        // 如果进程没有优雅退出，强制杀死
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);

        this.stopSpinner();

        if (hasErrors) {
          console.log(chalk.red('❌ Dev 模式检测到错误'));
          resolve(this.handleBuildError({ stdout: output, stderr: '' }));
        } else {
          console.log(chalk.green('✅ Dev 模式运行正常'));
          resolve({ success: true, output });
        }
      }, timeout);

      child.on('exit', (code) => {
        clearTimeout(timer);
        this.stopSpinner();

        if (code !== 0 || hasErrors) {
          console.log(chalk.red('❌ Dev 命令执行失败'));
          resolve(this.handleBuildError({ stdout: output, stderr: '' }));
        } else {
          console.log(chalk.green('✅ Dev 命令执行成功'));
          resolve({ success: true, output });
        }
      });
    });
  }

  /**
   * 检查文本中是否包含错误
   */
  containsErrors(text) {
    const errorPatterns = [
      /error/i,
      /ERROR/,
      /failed/i,
      /FAILED/,
      /cannot find module/i,
      /module not found/i,
      /syntax error/i,
      /type error/i,
      /compilation error/i,
      /build error/i
    ];

    return errorPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 执行构建命令
   */
  async executeBuild() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过构建执行'));
      return { success: true, output: 'DRY RUN - Build skipped' };
    }

    this.updateSpinner(`执行构建命令: ${this.options.buildCommand}`);
    this.buildStats.buildAttempts++;

    try {
      const output = execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return {
        success: true,
        output
      };
    } catch (error) {
      this.stopSpinner();
      console.log(chalk.red('❌ 构建失败'));
      const result = this.handleBuildError(error);
      this.startSpinner('继续处理...');
      return result;
    }
  }

  /**
   * 处理构建错误
   */
  handleBuildError(error) {
    const errorOutput = error.stdout + error.stderr;
    const errorSummary = this.extractErrorSummary(errorOutput);

    // 将完整错误日志写入文件
    this.logErrorToFile(errorOutput);

    if (errorSummary.length > 0) {
      console.log(chalk.red('\n构建错误摘要:'));
      // 只显示前20-30条错误给用户
      const displayCount = Math.min(25, errorSummary.length);
      errorSummary.slice(0, displayCount).forEach(line => {
        console.log(chalk.gray(`  ${line.trim()}`));
      });

      if (errorSummary.length > displayCount) {
        console.log(chalk.gray(`  ... 以及 ${errorSummary.length - displayCount} 个其他错误`));
        console.log(chalk.blue(`📄 完整错误日志已保存到: ${this.getErrorLogPath()}`));
      }
    } else {
      console.log(chalk.yellow('⚠️ 无法提取具体错误信息'));
      this.logBasicErrorInfo(errorOutput);
      this.suggestCommonSolutions(errorOutput);
    }

    return {
      success: false,
      output: errorOutput,
      errorSummary: errorSummary.join('\n'),
      error: error.message
    };
  }

  /**
   * 解析构建错误
   */
  parseErrors(buildOutput) {
    const errors = [];

    // 确保 buildOutput 是字符串
    const outputStr = typeof buildOutput === 'string' ? buildOutput : String(buildOutput || '');
    const lines = outputStr.split('\n');

    let currentError = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检测错误开始
      const errorMatch = this.detectErrorStart(line);
      if (errorMatch) {
        if (currentError) {
          errors.push(currentError);
        }
        currentError = {
          type: errorMatch.type,
          file: errorMatch.file,
          line: errorMatch.line,
          column: errorMatch.column,
          message: errorMatch.message,
          fullMessage: [line],
          severity: errorMatch.severity || 'error'
        };
      } else if (currentError && this.isErrorContinuation(line)) {
        currentError.fullMessage.push(line);
        if (line.trim()) {
          currentError.message += ' ' + line.trim();
        }
      } else if (currentError) {
        errors.push(currentError);
        currentError = null;
      }
    }

    if (currentError) {
      errors.push(currentError);
    }

    return this.categorizeErrors(errors);
  }

  /**
   * 检测错误开始 - 改进版本，支持更多错误类型和更好的解析
   */
  detectErrorStart(line) {
    // 清理行内容
    const cleanLine = line.trim();
    if (!cleanLine) return null;

    // TypeScript 错误 - 更宽松的匹配
    const tsError = cleanLine.match(/(.+\.(?:ts|vue))\((\d+),(\d+)\):\s*(error|warning)\s*(?:TS(\d+):)?\s*(.+)/);
    if (tsError) {
      return {
        type: 'typescript',
        file: this.normalizeFilePath(tsError[1]),
        line: parseInt(tsError[2]),
        column: parseInt(tsError[3]),
        severity: tsError[4],
        code: tsError[5] || 'unknown',
        message: tsError[6],
        rawLine: line
      };
    }

    // Vue 编译错误
    const vueError = cleanLine.match(/(.+\.vue):(\d+):(\d+):\s*(.*)/);
    if (vueError) {
      return {
        type: 'vue',
        file: this.normalizeFilePath(vueError[1]),
        line: parseInt(vueError[2]),
        column: parseInt(vueError[3]),
        message: vueError[4],
        rawLine: line
      };
    }

    // Webpack/Vite 模块错误 - 改进解析
    const moduleError = cleanLine.match(/ERROR in (.+)/);
    if (moduleError) {
      const errorPath = moduleError[1];
      // 尝试提取文件路径和位置信息
      const fileMatch = errorPath.match(/^([^:]+\.(js|ts|vue|css|scss|sass))(?::(\d+):(\d+))?/);
      if (fileMatch) {
        return {
          type: 'webpack',
          file: this.normalizeFilePath(fileMatch[1]),
          line: fileMatch[3] ? parseInt(fileMatch[3]) : null,
          column: fileMatch[4] ? parseInt(fileMatch[4]) : null,
          message: cleanLine,
          rawLine: line
        };
      } else {
        return {
          type: 'webpack',
          file: this.normalizeFilePath(errorPath),
          message: cleanLine,
          rawLine: line
        };
      }
    }

    // 模块未找到错误 - 专门处理，改进正则表达式
    const moduleNotFoundError = cleanLine.match(/Module not found.*Can't resolve ['"]([^'"]+)['"](?:.*in ['"]([^'"]+)['"])?/);
    if (moduleNotFoundError) {
      return {
        type: 'module-not-found',
        module: moduleNotFoundError[1],
        context: moduleNotFoundError[2] || 'unknown',
        message: cleanLine,
        rawLine: line
      };
    }

    // 简化的模块错误匹配
    const simpleModuleError = cleanLine.match(/Can't resolve ['"]([^'"]+)['"]/);
    if (simpleModuleError) {
      return {
        type: 'module-not-found',
        module: simpleModuleError[1],
        message: cleanLine,
        rawLine: line
      };
    }

    // ESLint 错误
    const eslintError = cleanLine.match(/(.+\.(?:js|vue|ts)):(\d+):(\d+):\s*(error|warning)\s*(.+)/);
    if (eslintError) {
      return {
        type: 'eslint',
        file: this.normalizeFilePath(eslintError[1]),
        line: parseInt(eslintError[2]),
        column: parseInt(eslintError[3]),
        severity: eslintError[4],
        message: eslintError[5],
        rawLine: line
      };
    }

    // 依赖解析错误
    const dependencyError = cleanLine.match(/Cannot find module ['"]([^'"]+)['"]|Cannot resolve dependency/);
    if (dependencyError) {
      return {
        type: 'dependency',
        module: dependencyError[1] || 'unknown',
        message: cleanLine,
        rawLine: line,
        file: null // 依赖错误通常没有特定文件
      };
    }

    // 通用错误
    const genericError = cleanLine.match(/Error:\s*(.+)/);
    if (genericError) {
      return {
        type: 'generic',
        message: genericError[1],
        rawLine: line,
        file: null // 通用错误可能没有特定文件
      };
    }

    return null;
  }

  /**
   * 标准化文件路径
   */
  normalizeFilePath(filePath) {
    if (!filePath) return null;

    // 移除绝对路径前缀，只保留相对路径
    let relativePath = filePath;

    // 如果是绝对路径，尝试提取相对于项目根目录的路径
    if (filePath.includes(this.projectPath)) {
      relativePath = filePath.replace(this.projectPath + '/', '');
    } else {
      // 尝试匹配常见的项目结构模式
      const projectStructureMatch = filePath.match(/.*\/(src\/.*|components\/.*|pages\/.*|views\/.*|assets\/.*|styles\/.*)$/);
      if (projectStructureMatch) {
        relativePath = projectStructureMatch[1];
      } else {
        // 如果路径包含目录分隔符，尝试提取最后几个部分
        const parts = filePath.split('/');
        if (parts.length > 1) {
          // 查找 src 目录的位置
          const srcIndex = parts.findIndex(part => part === 'src');
          if (srcIndex !== -1) {
            relativePath = parts.slice(srcIndex).join('/');
          } else {
            // 如果没有找到 src，取最后两个部分
            relativePath = parts.slice(-2).join('/');
          }
        }
      }
    }

    return relativePath || filePath;
  }

  /**
   * 检查是否为错误信息的继续
   */
  isErrorContinuation(line) {
    return line.startsWith('    ') || line.startsWith('\t') ||
           line.includes('at ') || line.includes('in ');
  }

  /**
   * 分类错误 - 改进版本，提供更精确的分类和用户友好的描述
   */
  categorizeErrors(errors) {
    return errors.map(error => {
      const message = error.message || '';
      const type = error.type || '';
      const module = error.module || '';

      // 依赖相关错误（最高优先级）
      if (type === 'module-not-found' || type === 'dependency' ||
          message.includes('Cannot find module') ||
          message.includes('Module not found')) {

        // 确保模块名称存在
        const moduleName = module || this.extractModuleFromMessage(message);

        // 检查是否是已知的迁移模块
        if (moduleName && moduleName.includes('element-ui')) {
          error.category = 'ui-library';
          error.friendlyName = 'Element UI 迁移';
          error.description = 'Element UI 需要升级到 Element Plus';
          error.autoFixable = true;
          error.solution = '自动替换为 Element Plus 导入';
          error.module = moduleName; // 确保模块名称被设置
        } else if (moduleName && moduleName.includes('vue-router') && !moduleName.includes('@4')) {
          error.category = 'missing-dependency';
          error.friendlyName = 'Vue Router 版本';
          error.description = 'Vue Router 需要升级到 v4';
          error.autoFixable = true;
          error.solution = '安装 vue-router@4 并更新语法';
          error.module = moduleName;
        } else if (moduleName && moduleName.includes('vuex')) {
          error.category = 'missing-dependency';
          error.friendlyName = 'Vuex 状态管理';
          error.description = 'Vuex 需要升级或迁移到 Pinia';
          error.autoFixable = false;
          error.solution = '建议迁移到 Pinia 或升级 Vuex';
          error.module = moduleName;
        } else {
          error.category = 'missing-module';
          error.friendlyName = '缺失模块';
          error.description = `无法找到模块: ${moduleName || '未知模块'}`;
          error.autoFixable = false;
          error.solution = '检查模块名称和安装状态';
          error.module = moduleName;
        }
      }
      // Vue 版本兼容性错误
      else if (message.includes('Vue') || message.includes('$mount') ||
               message.includes('Vue.extend') || message.includes('new Vue')) {
        error.category = 'vue-version';
        error.friendlyName = 'Vue 版本兼容性';
        error.description = 'Vue 2 语法需要更新为 Vue 3';
        error.autoFixable = true;
        error.solution = '使用 createApp 替代 new Vue';
      }
      // TypeScript 类型错误
      else if (type === 'typescript' || message.includes('TS') ||
               message.includes('Property') && message.includes('does not exist')) {
        error.category = 'typescript';
        error.friendlyName = 'TypeScript 类型';
        error.description = 'TypeScript 类型定义问题';
        error.autoFixable = true;
        error.solution = '更新类型定义或添加类型声明';
      }
      // UI 库相关错误
      else if (message.includes('element-ui') || message.includes('element-plus') ||
               message.includes('el-')) {
        error.category = 'ui-library';
        error.friendlyName = 'UI 组件库';
        error.description = 'UI 组件库兼容性问题';
        error.autoFixable = true;
        error.solution = '更新组件导入和使用方式';
      }
      // 语法错误
      else if (message.includes('Syntax') || message.includes('syntax') ||
               message.includes('Unexpected token')) {
        error.category = 'syntax-error';
        error.friendlyName = '语法错误';
        error.description = '代码语法不正确';
        error.autoFixable = true;
        error.solution = '修复语法错误';
      }
      // 属性不存在错误
      else if (message.includes('Property') && message.includes('does not exist')) {
        error.category = 'property-not-exist';
        error.friendlyName = '属性不存在';
        error.description = '访问了不存在的属性或方法';
        error.autoFixable = true;
        error.solution = '更新属性访问方式或添加属性定义';
      }
      // 其他错误
      else {
        error.category = 'other';
        error.friendlyName = '其他错误';
        error.description = '需要进一步分析的错误';
        error.autoFixable = false;
        error.solution = '查看详细错误信息进行手动修复';
      }

      // 设置优先级
      error.priority = this.errorPriorities[error.category] || 99;

      // 设置修复策略
      error.fixStrategy = this.getFixStrategy(error);

      return error;
    });
  }

  /**
   * 获取修复策略
   */
  getFixStrategy(error) {
    const strategies = {
      'missing-dependency': 'install-and-update',
      'missing-module': 'module-mapping',
      'vue-version': 'syntax-transform',
      'ui-library': 'component-migration',
      'typescript': 'type-fix',
      'property-not-exist': 'api-update',
      'syntax-error': 'syntax-fix',
      'other': 'ai-analysis'
    };

    return strategies[error.category] || 'manual';
  }

  /**
   * 从错误消息中提取模块名称
   */
  extractModuleFromMessage(message) {
    if (!message) return null;

    const patterns = [
      /Can't resolve ['"]([^'"]+)['"]/,
      /Cannot find module ['"]([^'"]+)['"]/,
      /Module not found.*['"]([^'"]+)['"]/,
      /resolve ['"]([^'"]+)['"]/
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * 修复错误 - 改进版本，带进度显示和详细反馈
   */
  async fixErrorsWithProgress(errors) {
    let fixedCount = 0;
    const fixDetails = [];
    const totalErrors = errors.length;

    console.log(chalk.blue(`\n开始修复 ${totalErrors} 个错误...`));

    for (let i = 0; i < errors.length; i++) {
      const error = errors[i];
      const progress = `[${i + 1}/${totalErrors}]`;

      try {
        console.log(chalk.gray(`${progress} 🔧 修复: ${error.friendlyName || error.category}`));

        if (error.file) {
          console.log(chalk.gray(`     文件: ${error.file}`));
        }

        if (error.description) {
          console.log(chalk.gray(`     问题: ${error.description}`));
        }

        const fixResult = await this.fixSingleError(error);

        if (fixResult.success) {
          fixedCount++;
          console.log(chalk.green(`     ✅ 修复成功: ${fixResult.description || '已修复'}`));

          fixDetails.push({
            file: error.file,
            category: error.category,
            description: fixResult.description || '修复成功',
            success: true
          });
        } else {
          console.log(chalk.yellow(`     ⚠️  修复失败: ${fixResult.reason || '未知原因'}`));

          fixDetails.push({
            file: error.file,
            category: error.category,
            description: fixResult.reason || '修复失败',
            success: false
          });
        }
      } catch (fixError) {
        console.log(chalk.red(`     ❌ 修复异常: ${fixError.message}`));

        fixDetails.push({
          file: error.file,
          category: error.category,
          description: `修复异常: ${fixError.message}`,
          success: false
        });
      }
    }

    return {
      fixed: fixedCount,
      total: totalErrors,
      details: fixDetails
    };
  }

  /**
   * 兼容旧版本的修复方法
   */
  async fixErrors(errors) {
    const result = await this.fixErrorsWithProgress(errors);
    return { fixed: result.fixed };
  }

  /**
   * 修复单个错误 - 改进版本，返回详细的修复结果
   */
  async fixSingleError(error) {
    // 如果错误没有文件路径，尝试从错误信息中提取
    if (!error.file && error.fullMessage) {
      const fileMatch = error.fullMessage.join(' ').match(/([^/\s]+\.(?:scss|sass|css|vue|js|ts))/);
      if (fileMatch) {
        error.file = fileMatch[1];
      }
    }

    // 如果错误没有文件路径，尝试从原始行中提取
    if (!error.file && error.rawLine) {
      const fileMatch = error.rawLine.match(/ERROR in ([^:]+\.(js|ts|vue|css|scss|sass))/);
      if (fileMatch) {
        error.file = this.normalizeFilePath(fileMatch[1]);
      }
    }

    // 确保文件路径存在且有效
    if (error.file && !error.file.includes('/')) {
      // 尝试在常见目录中查找文件
      const possiblePaths = [
        `src/${error.file}`,
        `src/components/${error.file}`,
        `src/views/${error.file}`,
        `src/pages/${error.file}`
      ];

      for (const possiblePath of possiblePaths) {
        const fullPath = path.join(this.projectPath, possiblePath);
        if (await fs.pathExists(fullPath)) {
          error.file = possiblePath;
          break;
        }
      }
    }

    // 对于某些错误类型，如果没有文件路径，提供特殊处理
    if (!error.file) {
      return this.handleErrorWithoutFile(error);
    }

    try {
      switch (error.category) {
        case 'missing-dependency':
          return await this.fixMissingDependency(error);
        case 'missing-module':
          return await this.fixMissingModule(error);
        case 'vue-version':
          return await this.fixVueVersionIssue(error);
        case 'ui-library':
          return await this.fixUILibraryIssue(error);
        case 'typescript':
          return await this.fixTypescriptError(error);
        case 'property-not-exist':
          return await this.fixPropertyNotExist(error);
        case 'syntax-error':
          return await this.fixSyntaxError(error);
        default:
          return await this.fixWithAI(error);
      }
    } catch (fixError) {
      return {
        success: false,
        reason: `修复过程中发生错误: ${fixError.message}`,
        error: fixError
      };
    }
  }

  /**
   * 修复缺失依赖错误
   */
  async fixMissingDependency(error) {
    const module = error.module || '';

    // 检查是否是已知的迁移依赖
    const migrationMap = {
      'vue-router': {
        newPackage: 'vue-router@4',
        description: '升级到 Vue Router 4'
      },
      'vuex': {
        newPackage: 'pinia',
        description: '建议迁移到 Pinia'
      },
      'element-ui': {
        newPackage: 'element-plus',
        description: '升级到 Element Plus'
      }
    };

    if (migrationMap[module]) {
      const migration = migrationMap[module];
      return {
        success: false,
        reason: `需要手动安装: ${migration.newPackage}`,
        description: migration.description,
        suggestion: `运行: npm install ${migration.newPackage}`
      };
    }

    return {
      success: false,
      reason: `未知依赖: ${module}`,
      suggestion: '请检查依赖名称和版本'
    };
  }

  /**
   * 修复 TypeScript 错误
   */
  async fixTypescriptError(error) {
    if (!error.file) {
      return {
        success: false,
        reason: '无法确定文件路径'
      };
    }

    // 对于常见的 TypeScript 错误，尝试使用 AI 修复
    return await this.fixWithAI(error);
  }

  /**
   * 修复语法错误
   */
  async fixSyntaxError(error) {
    if (!error.file) {
      return {
        success: false,
        reason: '无法确定文件路径'
      };
    }

    // 语法错误通常需要 AI 分析
    return await this.fixWithAI(error);
  }

  /**
   * 处理没有文件路径的错误
   */
  handleErrorWithoutFile(error) {
    const category = error.category || 'other';

    switch (category) {
      case 'missing-dependency':
      case 'missing-module':
        return {
          success: false,
          reason: '这是一个依赖问题，需要手动安装相关包',
          suggestion: error.module ?
            `尝试运行: npm install ${error.module}` :
            '检查 package.json 中的依赖配置'
        };

      case 'ui-library':
        return {
          success: false,
          reason: '这是一个 UI 库迁移问题',
          suggestion: '需要手动安装 Element Plus 并更新导入语句'
        };

      default:
        return {
          success: false,
          reason: '无法确定具体的文件路径',
          suggestion: '这可能是一个全局配置问题，请检查项目配置文件'
        };
    }
  }

  /**
   * 修复缺失模块错误 - 改进版本
   */
  async fixMissingModule(error) {
    const moduleName = error.module || this.extractModuleName(error.message);

    if (!moduleName) {
      return {
        success: false,
        reason: '无法提取模块名称'
      };
    }

    if (!error.file) {
      return {
        success: false,
        reason: '无法确定文件路径'
      };
    }

    // 获取模块映射表
    const moduleMapping = this.getModuleMapping();

    if (Object.prototype.hasOwnProperty.call(moduleMapping, moduleName)) {
      const replacement = moduleMapping[moduleName];

      if (replacement) {
        const replaceResult = await this.replaceImport(error.file, moduleName, replacement);
        return {
          success: replaceResult,
          description: replaceResult ? `已替换 ${moduleName} 为 ${replacement}` : '替换失败',
          reason: replaceResult ? null : '文件替换操作失败'
        };
      } else {
        const removeResult = await this.removeImport(error.file, moduleName);
        return {
          success: removeResult,
          description: removeResult ? `已移除 ${moduleName} 导入` : '移除失败',
          reason: removeResult ? null : '文件移除操作失败'
        };
      }
    }

    // 如果不在映射表中，尝试使用 AI 修复
    return await this.fixWithAI(error);
  }

  /**
   * 从错误消息中提取模块名称
   */
  extractModuleName(message) {
    if (!message) return null;

    const patterns = [
      /Cannot find module ['"]([^'"]+)['"]/,
      /Module not found.*['"]([^'"]+)['"]/,
      /Can't resolve ['"]([^'"]+)['"]/
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * 修复属性不存在错误
   */
  async fixPropertyNotExist(error) {
    // 对于属性不存在的错误，通常需要 AI 分析
    return await this.fixWithAI(error);
  }

  /**
   * 修复 Vue 版本问题
   */
  async fixVueVersionIssue(error) {
    if (!error.file) {
      return {
        success: false,
        reason: '无法确定文件路径'
      };
    }

    // 对于 Vue 版本兼容性问题，使用 AI 修复
    const result = await this.fixWithAI(error);

    // 如果 AI 修复失败，提供手动修复建议
    if (!result.success) {
      result.suggestion = 'Vue 2 到 Vue 3 迁移建议: 使用 createApp 替代 new Vue，更新生命周期钩子';
    }

    return result;
  }

  /**
   * 修复 UI 库问题
   */
  async fixUILibraryIssue(error) {
    if (!error.file) {
      return {
        success: false,
        reason: '无法确定文件路径'
      };
    }

    // 对于 Element UI 相关的错误，先尝试简单的字符串替换
    if (error.message && error.message.includes('element-ui')) {
      try {
        const filePath = path.join(this.projectPath, error.file);
        if (await fs.pathExists(filePath)) {
          const content = await fs.readFile(filePath, 'utf8');

          // 简单的 Element UI 到 Element Plus 替换
          let newContent = content
            .replace(/from ['"]element-ui['"]/g, "from 'element-plus'")
            .replace(/import ElementUI from ['"]element-ui['"]/g, "import ElementPlus from 'element-plus'")
            .replace(/Vue\.use\(ElementUI\)/g, 'app.use(ElementPlus)');

          if (newContent !== content) {
            await fs.writeFile(filePath, newContent, 'utf8');
            return {
              success: true,
              description: '已更新 Element UI 导入为 Element Plus'
            };
          }
        }
      } catch (replaceError) {
        // 如果简单替换失败，回退到 AI 修复
      }
    }

    // 回退到 AI 修复
    const result = await this.fixWithAI(error);

    if (!result.success) {
      result.suggestion = 'UI 库迁移建议: 安装 Element Plus 并更新组件导入和使用方式';
    }

    return result;
  }

  /**
   * 使用 AI 修复错误 - 改进版本
   */
  async fixWithAI(error) {
    if (!error.file) {
      return {
        success: false,
        reason: '无法确定文件路径，AI 修复需要具体的文件'
      };
    }

    // 检查文件是否存在
    const filePath = path.join(this.projectPath, error.file);
    if (!await fs.pathExists(filePath)) {
      return {
        success: false,
        reason: `文件不存在: ${error.file}`
      };
    }

    // 优先使用内置 AI 服务
    if (this.isEnabled()) {
      try {
        const result = await this.repairBuildError(error);

        if (result.success) {
          return {
            success: true,
            description: `AI 修复成功: ${error.file}`,
            details: result
          };
        } else {
          return {
            success: false,
            reason: `AI 修复失败: ${result.error || '未知错误'}`,
            details: result
          };
        }
      } catch (aiError) {
        return {
          success: false,
          reason: `AI 修复异常: ${aiError.message}`
        };
      }
    }

    // 回退到外部 aiRepairer
    if (this.options.aiRepairer && this.options.aiRepairer.isEnabled()) {
      try {
        const result = await this.options.aiRepairer.repairSingleFile({
          file: error.file,
          absolutePath: filePath,
          error: error.message
        }, this.projectPath);

        return {
          success: result.success,
          description: result.success ? '外部 AI 修复成功' : '外部 AI 修复失败',
          reason: result.success ? null : result.error
        };
      } catch (aiError) {
        return {
          success: false,
          reason: `外部 AI 修复异常: ${aiError.message}`
        };
      }
    }

    return {
      success: false,
      reason: 'AI 服务不可用',
      suggestion: '请配置 AI 服务或手动修复此错误'
    };
  }

  /**
   * 使用 AI 修复构建错误
   */
  async repairBuildError(error) {
    // 验证错误对象和文件路径
    if (!error || !error.file) {
      throw new Error('错误对象缺少文件路径信息');
    }

    const filePath = path.join(this.projectPath, error.file);

    try {
      // 验证文件路径是否存在
      if (!await fs.pathExists(filePath)) {
        throw new Error(`文件不存在: ${error.file}`);
      }

      // 读取原始文件内容
      const originalContent = await fs.readFile(filePath, 'utf8');

      // 生成构建错误专用提示
      const prompt = this.generateBuildErrorPrompt(originalContent, error);

      // 调用 AI 进行修复
      const aiResponse = await this.callAI(prompt);

      // 尝试解析 XML 格式的响应
      const repairedContent = this.parseAIResponse(aiResponse, originalContent);

      // 验证修复结果
      if (this.validateRepairedContent(repairedContent, originalContent)) {
        // 备份原文件
        await this.backupFile(filePath);

        // 写入修复后的内容
        await fs.writeFile(filePath, repairedContent, 'utf8');

        console.log(chalk.green(`✅ 构建错误修复成功: ${error.file}`));
        return {
          file: error.file,
          success: true,
          originalSize: originalContent.length,
          repairedSize: repairedContent.length
        };
      } else {
        console.log(chalk.yellow(`⚠️  构建错误修复结果验证失败: ${error.file}`));
        return {
          file: error.file,
          success: false,
          error: 'Validation failed'
        };
      }
    } catch (repairError) {
      console.log(chalk.red(`❌ 构建错误修复失败: ${error.file}`));
      return {
        file: error.file,
        success: false,
        error: repairError.message
      };
    }
  }

  /**
   * 生成构建错误专用提示词
   */
  generateBuildErrorPrompt(originalContent, error) {
    const fileExtension = error.file ? path.extname(error.file) : '';
    const errorMessage = error.message;
    const errorType = error.type;
    const errorCategory = error.category;

    let prompt = `你是一个专业的 Vue 2 到 Vue 3 迁移专家，现在需要修复构建过程中出现的错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**错误信息**：
- 错误类型：${errorType}
- 错误分类：${errorCategory}
- 错误消息：${errorMessage}
- 文件路径：${error.file}
${error.line ? `- 错误行号：${error.line}` : ''}
${error.column ? `- 错误列号：${error.column}` : ''}

**修复要求**：
1. **保持功能一致性**：修复后的代码必须保持原有功能不变
2. **Vue 3 兼容性**：确保代码符合 Vue 3 语法和最佳实践
3. **Element Plus 兼容性**：将 Element UI 组件正确替换为 Element Plus
4. **TypeScript 兼容性**：如果是 TypeScript 文件，确保类型定义正确
5. **构建兼容性**：修复后的代码必须能够通过构建流程

**输出格式要求**：
请使用以下 XML 格式返回修复结果：

<file_edit>
<path>${error.file}</path>
<content>
修复后的完整文件内容
</content>
</file_edit>

**常见构建错误修复指南**：`;

    // 根据错误类型添加特定指导
    switch (errorCategory) {
      case 'missing-module':
        prompt += `

**缺失模块错误修复**：
- 检查模块导入路径是否正确
- 将 Vue 2 相关模块替换为 Vue 3 对应模块
- 将 Element UI 导入替换为 Element Plus
- 移除 Vue 3 中已内置的模块（如 @vue/composition-api）`;
        break;

      case 'property-not-exist':
        prompt += `

**属性不存在错误修复**：
- 检查 Vue 3 中是否移除了该属性
- 使用 Vue 3 的新 API 替换已废弃的属性
- 更新组件实例的属性访问方式
- 修复 this.$refs 的访问方式`;
        break;

      case 'vue-version':
        prompt += `

**Vue 版本兼容性错误修复**：
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新生命周期钩子名称（如 beforeDestroy → beforeUnmount）
- 修复事件处理器的语法变化`;
        break;

      case 'ui-library':
        prompt += `

**UI 库兼容性错误修复**：
- 将 Element UI 组件名替换为 Element Plus 对应组件
- 更新组件属性名称和事件名称
- 修复图标引用方式
- 更新主题和样式引用`;
        break;

      default:
        prompt += `

**通用构建错误修复**：
- 检查语法错误和拼写错误
- 确保导入导出语句正确
- 修复类型定义问题
- 解决依赖冲突`;
    }

    // 根据文件类型添加特定指导
    if (fileExtension === '.vue') {
      prompt += `

**Vue 文件特定修复要求**：
- 更新 <template> 中的 Element UI 组件为 Element Plus
- 在 <script> 中使用 defineComponent 或 Composition API
- 更新事件处理和 refs 访问方式
- 确保 props 和 emits 正确定义
- 修复 v-model 的使用方式`;
    } else if (fileExtension === '.js' || fileExtension === '.ts') {
      prompt += `

**JavaScript/TypeScript 文件特定修复要求**：
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新 Vue 插件注册方式
- 修复导入语句和模块路径
- 更新类型定义（TypeScript）`;
    }

    prompt += `

**原始代码**：
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

**输出要求**：
1. 请仔细分析错误原因，提供修复后的完整代码
2. 必须使用上述 XML 格式返回结果
3. 确保修复后的代码能够解决构建错误
4. 保持代码的可读性和维护性
5. 如果需要添加新的导入，请确保模块路径正确

请使用 XML 格式提供修复后的完整代码：`;

    return prompt;
  }

  /**
   * 替换导入语句
   */
  async replaceImport(filePath, oldModule, newModule) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;

      const content = await fs.readFile(fullPath, 'utf8');
      const newContent = content.replace(
        new RegExp(`(['"])${oldModule}\\1`, 'g'),
        `$1${newModule}$1`
      );

      if (newContent !== content) {
        await fs.writeFile(fullPath, newContent, 'utf8');
        console.log(chalk.green(`✅ 替换导入: ${oldModule} → ${newModule}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 替换导入失败: ${error.message}`));
    }

    return false;
  }

  /**
   * 移除导入语句
   */
  async removeImport(filePath, moduleName) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;

      const content = await fs.readFile(fullPath, 'utf8');
      const lines = content.split('\n');
      const newLines = lines.filter(line =>
        !line.includes(`'${moduleName}'`) && !line.includes(`"${moduleName}"`)
      );

      if (newLines.length !== lines.length) {
        await fs.writeFile(fullPath, newLines.join('\n'), 'utf8');
        console.log(chalk.green(`✅ 移除导入: ${moduleName}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 移除导入失败: ${error.message}`));
    }

    return false;
  }

  /**
   * 打印构建统计
   */
  printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.buildStats.buildAttempts} 次`);
    console.log(`发现错误: ${this.buildStats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.buildStats.errorsFixed} 个`);
    console.log(`最终状态: ${this.buildStats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);
  }

  /**
   * 验证修复后的内容（构建错误专用验证）
   */
  validateRepairedContent(repairedContent, originalContent) {
    // 调用基类的验证方法
    if (!super.validateRepairedContent(repairedContent, originalContent)) {
      return false;
    }

    // 构建错误特定的验证
    const buildErrorMarkers = ['COMPILATION_ERROR', 'BUILD_ERROR', 'SYNTAX_ERROR'];
    if (buildErrorMarkers.some(marker => repairedContent.includes(marker))) {
      return false;
    }

    // 检查是否包含基本的 Vue 组件结构
    if (originalContent.includes('<template>') && !repairedContent.includes('<template>')) {
      return false;
    }

    return true;
  }

  /**
   * 获取构建统计信息
   */
  getBuildStats() {
    return { ...this.buildStats };
  }

  /**
   * 提取错误摘要
   */
  extractErrorSummary(errorOutput) {
    return errorOutput.split('\n').filter(line =>
      line.includes('ERROR') ||
      line.includes('Error:') ||
      line.includes('error') ||
      line.includes('Failed')
    );
  }

  /**
   * 记录安装错误
   */
  logInstallError(error) {
    console.error(chalk.red('安装错误:'), error.message);

    if (this.options.verbose) {
      console.log(chalk.gray('详细错误信息:'));
      console.log(chalk.red(error.stdout + error.stderr));
    }
  }

  /**
   * 建议常见解决方案
   */
  suggestCommonSolutions(errorOutput) {
    if (errorOutput.includes('ETARGET') || errorOutput.includes('No matching version found')) {
      console.log(chalk.yellow('\n⚠️ 依赖包版本问题:'));
      console.log(chalk.gray('  可能存在一个或多个依赖包找不到匹配的版本'));
      console.log(chalk.gray('  建议: 检查 package.json 中指定的版本是否存在，或尝试使用兼容 Vue 3 的替代库'));
    }

    if (errorOutput.includes('peer dependency') || errorOutput.includes('ERESOLVE')) {
      console.log(chalk.yellow('\n⚠️ 依赖冲突问题:'));
      console.log(chalk.gray('  检测到 peer dependency 冲突，某些库可能需要特定版本的 Vue'));
      console.log(chalk.gray('  建议: 更新到兼容 Vue 3 的库版本，或使用 --force 强制安装'));
    }
  }

  /**
   * 输出基本错误信息
   * @private
   */
  logBasicErrorInfo(errorOutput) {
    // 如果启用了详细模式，显示一部分原始错误
    if (this.options && this.options.verbose) {
      const previewLength = Math.min(2000, errorOutput.length);
      console.log(chalk.gray('\n错误详情:'));
      console.log(chalk.red(errorOutput.substring(0, previewLength)));

      if (previewLength < errorOutput.length) {
        console.log(chalk.gray(`... (截断，共 ${errorOutput.length} 字符)`));
      }
    } else {
      console.log(chalk.yellow('提示: 使用 --verbose 参数可查看详细错误信息'));
    }
  }

  /**
   * 将错误日志写入文件
   */
  async logErrorToFile(errorOutput) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const logFileName = `build-error-${timestamp}.log`;
      const logPath = path.join(this.projectPath, 'build-fixer-logs', logFileName);

      // 确保日志目录存在
      await fs.ensureDir(path.dirname(logPath));

      // 写入错误日志
      const logContent = `Build Error Log - ${new Date().toISOString()}
=====================================

Project Path: ${this.projectPath}
Build Command: ${this.options.buildCommand}
Mode: ${this.options.mode}

Error Output:
${errorOutput}

=====================================
`;

      await fs.writeFile(logPath, logContent, 'utf8');
      this.currentErrorLogPath = logPath;

      if (this.options.verbose) {
        console.log(chalk.gray(`📄 错误日志已保存: ${logPath}`));
      }
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法保存错误日志: ${error.message}`));
    }
  }

  /**
   * 获取当前错误日志路径
   */
  getErrorLogPath() {
    return this.currentErrorLogPath || 'build-fixer-logs/';
  }

  /**
   * 解析 AI 响应，支持 XML 格式
   */
  parseAIResponse(aiResponse, originalContent) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = aiResponse.match(/<file_edit>\s*<path>.*?<\/path>\s*<content>([\s\S]*?)<\/content>\s*<\/file_edit>/);

      if (xmlMatch) {
        const content = xmlMatch[1].trim();
        console.log(chalk.blue('🤖 检测到 XML 格式的 AI 响应'));
        return content;
      }

      // 尝试解析代码块格式
      const codeBlockMatch = aiResponse.match(/```(?:javascript|js|vue|ts|typescript)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        const content = codeBlockMatch[1].trim();
        console.log(chalk.blue('🤖 检测到代码块格式的 AI 响应'));
        return content;
      }

      // 如果没有特殊格式，直接返回响应内容
      console.log(chalk.yellow('⚠️  AI 响应格式未识别，使用原始内容'));
      return aiResponse.trim();
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  解析 AI 响应失败: ${error.message}`));
      return originalContent;
    }
  }

}

module.exports = BuildFixer;
