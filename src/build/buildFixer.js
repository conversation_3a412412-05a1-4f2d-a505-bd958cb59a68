const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const { AIService } = require('../ai/ai-service');
const ConfigLoader = require('./configLoader');

/**
 * AI Agent 构建错误修复器
 * 基于强大的 AI Agent 能力，自动分析和修复构建错误
 *
 * 核心特性：
 * - AI 驱动的错误分析，无需预定义分类
 * - 两阶段修复：文件选择 → 文件修改
 * - 工具调用机制，类似 Augment Agent
 * - 简化的配置和更强的适应性
 */

class BuildFixer extends AIService {
  constructor(projectPath, options = {}) {
    super(options);

    this.projectPath = projectPath;
    this.userOptions = options;
    this.options = {};

    // 配置加载器
    this.configLoader = new ConfigLoader();

    // 构建统计
    this.buildStats = {
      startTime: null,
      endTime: null,
      duration: 0,
      buildAttempts: 0,
      buildSuccess: false,
      finalBuildSuccess: false,
      errorsFound: [],
      errorsFixed: 0
    };

    // Spinner 状态
    this.spinner = null;
    this.interactiveMode = options.interactive || false;

    // 工具定义 - 类似 Augment 的工具系统
    this.tools = [
      {
        name: 'read_file',
        description: '读取项目中的文件内容',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            }
          },
          required: ['file_path']
        }
      },
      {
        name: 'write_file',
        description: '写入或修改项目中的文件',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            },
            content: {
              type: 'string',
              description: '要写入的文件内容'
            }
          },
          required: ['file_path', 'content']
        }
      },
      {
        name: 'list_files',
        description: '列出项目目录中的文件',
        parameters: {
          type: 'object',
          properties: {
            directory: {
              type: 'string',
              description: '要列出的目录路径，相对于项目根目录'
            },
            pattern: {
              type: 'string',
              description: '文件匹配模式，如 \'*.vue\' 或 \'*.js\''
            }
          },
          required: ['directory']
        }
      }
    ];
  }

  // Spinner 管理方法
  startSpinner(text) {
    if (this.spinner) {
      this.spinner.stop();
    }
    this.spinner = ora(text).start();
  }

  updateSpinner(text) {
    if (this.spinner) {
      this.spinner.text = text;
    }
  }

  succeedSpinner(text) {
    if (this.spinner) {
      this.spinner.succeed(text);
      this.spinner = null;
    }
  }

  failSpinner(text) {
    if (this.spinner) {
      this.spinner.fail(text);
      this.spinner = null;
    }
  }

  stopSpinner() {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }

  /**
   * 执行构建并修复错误 - 主入口方法
   */
  async buildAndFix() {
    this.buildStats.startTime = Date.now();

    try {
      this.startSpinner('开始构建项目并修复错误...');

      // 初始化
      await this.initialize();

      // 根据模式执行不同的构建策略
      let buildResult;
      if (this.options.mode === 'dev') {
        this.updateSpinner('执行 dev 模式错误检测...');
        buildResult = await this.performDevCheck();
      } else {
        this.updateSpinner('执行构建...');
        buildResult = await this.performBuild();
      }

      if (buildResult.success) {
        this.succeedSpinner(`${this.options.mode === 'dev' ? 'Dev 检测' : '项目构建'}成功！`);
        this.buildStats.buildSuccess = true;
        this.buildStats.finalBuildSuccess = true;
        return this.createResult(true);
      }

      // 使用 AI Agent 分析和修复错误
      this.updateSpinner('AI Agent 正在分析构建错误...');
      const fixResult = await this.analyzeAndFixWithAI(buildResult);

      this.stopSpinner();
      this.printBuildStats();
      return fixResult;
    } catch (error) {
      this.failSpinner('构建修复过程失败');
      console.error(chalk.red('❌ 错误详情:'), error.message);
      throw error;
    } finally {
      this.buildStats.endTime = Date.now();
      this.buildStats.duration = this.buildStats.endTime - this.buildStats.startTime;
      this.stopSpinner();
    }
  }

  /**
   * 初始化构建修复器
   */
  async initialize() {
    // 加载配置文件并与用户选项合并
    const configFileOptions = await this.configLoader.loadConfig(
      this.userOptions.configPath,
      this.projectPath
    );

    // 合并配置：用户选项 > 配置文件 > 默认值
    this.options = {
      buildCommand: 'npm run build',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      maxAttempts: 3,
      mode: 'build',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      interactive: false,
      explain: false,
      verbose: false,
      ...configFileOptions,
      ...this.userOptions
    };

    console.log(chalk.blue('🔧 构建修复器配置:'));
    console.log(chalk.gray(`  项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`  构建命令: ${this.options.buildCommand}`));
    console.log(chalk.gray(`  运行模式: ${this.options.mode}`));
    console.log(chalk.gray(`  最大尝试: ${this.options.maxAttempts}`));
    console.log(chalk.gray(`  AI 修复: ${this.options.skipAI ? '禁用' : '启用'}`));
  }

  /**
   * 使用 AI Agent 分析和修复错误
   */
  async analyzeAndFixWithAI(buildResult) {
    if (this.options.skipAI) {
      console.log(chalk.yellow('⚠️  跳过 AI 修复步骤'));
      return this.createResult(false, 'AI 修复被跳过');
    }

    if (!this.isEnabled()) {
      console.log(chalk.yellow('⚠️  AI 服务不可用，无法进行智能修复'));
      return this.createResult(false, 'AI 服务不可用');
    }

    // 收集构建错误信息
    let buildOutput = buildResult.output || '';
    console.log(chalk.blue('\n🤖 AI Agent 开始分析构建错误...'));

    // 尝试修复错误
    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxAttempts}...`));

      const fixResult = await this.performAIFix(buildOutput, attempt);

      if (fixResult.filesModified > 0) {
        console.log(chalk.green(`✅ AI Agent 修改了 ${fixResult.filesModified} 个文件`));
        this.buildStats.errorsFixed += fixResult.filesModified;

        // 重新构建验证修复效果
        console.log(chalk.blue('\n🔄 重新构建项目验证修复效果...'));
        const newBuildResult = await this.performBuild();

        if (newBuildResult.success) {
          console.log(chalk.green('🎉 构建成功！所有错误已修复'));
          this.buildStats.finalBuildSuccess = true;
          this.displaySuccessMessage();
          return this.createResult(true);
        } else {
          console.log(chalk.yellow('⚠️  构建仍有问题，准备下一轮修复...'));
          buildOutput = newBuildResult.output;
        }
      } else {
        console.log(chalk.yellow('⚠️  本轮 AI Agent 未能修复任何文件'));
      }
    }

    console.log(chalk.red(`❌ 经过 ${this.options.maxAttempts} 次尝试，仍无法完全修复构建错误`));
    return this.createResult(false, `AI 修复未能完全解决问题，已尝试 ${this.options.maxAttempts} 次`);
  }

  /**
   * 执行 AI 修复 - 两阶段过程
   */
  async performAIFix(buildOutput) {
    try {
      // 第一阶段：让 AI 分析错误并选择要查看的文件
      console.log(chalk.gray('  📋 阶段1: AI 分析错误并选择相关文件...'));
      const analysisResult = await this.analyzeErrorsWithAI(buildOutput);

      if (!analysisResult.success) {
        console.log(chalk.yellow('  ⚠️  AI 错误分析失败'));
        return { filesModified: 0, error: analysisResult.error };
      }

      // 第二阶段：让 AI 修复选定的文件
      console.log(chalk.gray('  🔧 阶段2: AI 修复相关文件...'));
      const fixResult = await this.fixFilesWithAI(analysisResult.filesToFix, buildOutput);

      return fixResult;
    } catch (error) {
      console.error(chalk.red('  ❌ AI 修复过程异常:'), error.message);
      return { filesModified: 0, error: error.message };
    }
  }

  /**
   * 第一阶段：AI 分析错误并选择相关文件
   */
  async analyzeErrorsWithAI(buildOutput) {
    try {
      const prompt = this.generateAnalysisPrompt(buildOutput);
      const response = await this.callAI(prompt);

      // 解析 AI 响应，提取要修复的文件列表
      const filesToFix = this.parseAnalysisResponse(response);

      if (filesToFix.length === 0) {
        return {
          success: false,
          error: 'AI 未能识别需要修复的文件'
        };
      }

      console.log(chalk.gray(`    ✅ AI 识别出 ${filesToFix.length} 个需要修复的文件`));
      filesToFix.forEach(file => {
        console.log(chalk.gray(`      - ${file}`));
      });

      return {
        success: true,
        filesToFix
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 第二阶段：AI 修复选定的文件
   */
  async fixFilesWithAI(filesToFix, buildOutput) {
    let filesModified = 0;
    const errors = [];

    for (const filePath of filesToFix) {
      try {
        console.log(chalk.gray(`    🔧 修复文件: ${filePath}`));

        // 读取文件内容
        const fileContent = await this.executeToolCall('read_file', { file_path: filePath });

        if (!fileContent.success) {
          console.log(chalk.yellow(`      ⚠️  无法读取文件: ${fileContent.error}`));
          errors.push(`无法读取文件 ${filePath}: ${fileContent.error}`);
          continue;
        }

        // 让 AI 修复文件
        const fixResult = await this.fixSingleFileWithAI(filePath, fileContent.content, buildOutput);

        if (fixResult.success) {
          // 写入修复后的文件
          const writeResult = await this.executeToolCall('write_file', {
            file_path: filePath,
            content: fixResult.fixedContent
          });

          if (writeResult.success) {
            console.log(chalk.green(`      ✅ 文件修复成功`));
            filesModified++;
          } else {
            console.log(chalk.yellow(`      ⚠️  无法写入文件: ${writeResult.error}`));
            errors.push(`无法写入文件 ${filePath}: ${writeResult.error}`);
          }
        } else {
          console.log(chalk.yellow(`      ⚠️  AI 修复失败: ${fixResult.error}`));
          errors.push(`AI 修复失败 ${filePath}: ${fixResult.error}`);
        }
      } catch (error) {
        console.log(chalk.red(`      ❌ 修复异常: ${error.message}`));
        errors.push(`修复异常 ${filePath}: ${error.message}`);
      }
    }

    return {
      filesModified,
      errors,
      totalFiles: filesToFix.length
    };
  }

  /**
   * 执行工具调用
   */
  async executeToolCall(toolName, parameters) {
    try {
      switch (toolName) {
        case 'read_file':
          return await this.readFile(parameters.file_path);
        case 'write_file':
          return await this.writeFile(parameters.file_path, parameters.content);
        case 'list_files':
          return await this.listFiles(parameters.directory, parameters.pattern);
        default:
          return {
            success: false,
            error: `未知的工具: ${toolName}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 读取文件工具
   */
  async readFile(filePath) {
    try {
      const fullPath = path.join(this.projectPath, filePath);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '文件不存在'
        };
      }

      const content = await fs.readFile(fullPath, 'utf8');
      return {
        success: true,
        content
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 写入文件工具
   */
  async writeFile(filePath, content) {
    try {
      if (this.options.dryRun) {
        console.log(chalk.gray(`      [预览模式] 将写入文件: ${filePath}`));
        return {
          success: true,
          message: '预览模式，未实际写入'
        };
      }

      const fullPath = path.join(this.projectPath, filePath);

      // 确保目录存在
      await fs.ensureDir(path.dirname(fullPath));

      // 备份原文件
      if (await fs.pathExists(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        await fs.copy(fullPath, backupPath);
      }

      await fs.writeFile(fullPath, content, 'utf8');
      return {
        success: true,
        message: '文件写入成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 列出文件工具
   */
  async listFiles(directory, pattern) {
    try {
      const fullPath = path.join(this.projectPath, directory);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '目录不存在'
        };
      }

      const files = await fs.readdir(fullPath);
      let filteredFiles = files;

      if (pattern) {
        const glob = require('glob');
        filteredFiles = files.filter(file => glob.minimatch(file, pattern));
      }

      return {
        success: true,
        files: filteredFiles
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成错误分析提示词
   */
  generateAnalysisPrompt(buildOutput) {
    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家和构建错误分析师。请分析以下构建错误输出，并确定需要修复的文件。

**任务目标**：
1. 分析构建错误输出
2. 识别导致错误的具体文件
3. 返回需要修复的文件路径列表

**构建错误输出**：
\`\`\`
${buildOutput}
\`\`\`

**工具可用**：
你可以使用以下工具来帮助分析：
${this.tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

**响应格式**：
请使用以下 XML 格式返回分析结果：

\`\`\`xml
<analysis>
<files_to_fix>
<file>src/components/Example.vue</file>
<file>src/utils/helper.js</file>
</files_to_fix>
<reasoning>
简要说明为什么选择这些文件进行修复
</reasoning>
</analysis>
\`\`\`

请仔细分析错误信息，重点关注：
- 文件路径和行号信息
- 模块导入错误
- Vue 2/3 兼容性问题
- TypeScript 类型错误
- 依赖包问题

只返回确实需要修改代码的文件，不要包含配置文件或依赖安装问题。`;
  }

  /**
   * 解析 AI 分析响应
   */
  parseAnalysisResponse(response) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<analysis>[\s\S]*?<files_to_fix>([\s\S]*?)<\/files_to_fix>[\s\S]*?<\/analysis>/);

      if (xmlMatch) {
        const filesSection = xmlMatch[1];
        const fileMatches = filesSection.match(/<file>(.*?)<\/file>/g);

        if (fileMatches) {
          return fileMatches.map(match => {
            const file = match.replace(/<\/?file>/g, '').trim();
            return file;
          }).filter(file => file.length > 0);
        }
      }

      // 回退：尝试从响应中提取文件路径
      const lines = response.split('\n');
      const files = [];

      for (const line of lines) {
        // 查找看起来像文件路径的行
        const fileMatch = line.match(/(?:src\/|\.\/)?[\w/\-.]+\.(vue|js|ts|jsx|tsx)$/);
        if (fileMatch) {
          files.push(fileMatch[0]);
        }
      }

      return [...new Set(files)]; // 去重
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 分析响应失败，使用空列表'));
      return [];
    }
  }

  /**
   * 修复单个文件
   */
  async fixSingleFileWithAI(filePath, fileContent, buildOutput) {
    try {
      const prompt = this.generateFixPrompt(filePath, fileContent, buildOutput);
      const response = await this.callAI(prompt);

      // 解析修复后的文件内容
      const fixedContent = this.parseFixResponse(response, fileContent);

      if (!fixedContent || fixedContent === fileContent) {
        return {
          success: false,
          error: 'AI 未能生成有效的修复内容'
        };
      }

      return {
        success: true,
        fixedContent
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成文件修复提示词
   */
  generateFixPrompt(filePath, fileContent, buildOutput) {
    const fileExtension = path.extname(filePath);

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请修复以下文件中的构建错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**文件信息**：
- 文件路径：${filePath}
- 文件类型：${fileExtension}

**构建错误输出**：
\`\`\`
${buildOutput}
\`\`\`

**当前文件内容**：
\`\`\`${fileExtension.slice(1) || 'text'}
${fileContent}
\`\`\`

**修复要求**：
1. 保持原有功能不变
2. 使用 Vue 3 兼容的语法
3. 更新导入语句和组件使用方式
4. 修复 TypeScript 类型错误
5. 确保代码风格一致

**响应格式**：
请使用以下 XML 格式返回修复后的完整文件内容：

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
修复后的完整文件内容
</content>
</file_fix>
\`\`\`

**常见修复模式**：
- Vue 2 → Vue 3: new Vue() → createApp()
- Element UI → Element Plus: 更新导入路径和组件名称
- Vue Router: 更新路由配置语法
- Vuex: 更新状态管理语法
- 组合式 API: 使用 ref, reactive, computed 等

请仔细分析错误信息，提供准确的修复方案。`;
  }

  /**
   * 解析修复响应
   */
  parseFixResponse(response, originalContent) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<file_fix>[\s\S]*?<content>([\s\S]*?)<\/content>[\s\S]*?<\/file_fix>/);

      if (xmlMatch) {
        const content = xmlMatch[1].trim();
        if (content && content !== originalContent) {
          return content;
        }
      }

      // 回退：尝试解析代码块格式
      const codeBlockMatch = response.match(/```(?:vue|js|ts|javascript|typescript)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        const content = codeBlockMatch[1].trim();
        if (content && content !== originalContent) {
          return content;
        }
      }

      return null;
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 修复响应失败'));
      return null;
    }
  }

  /**
   * 显示成功消息
   */
  displaySuccessMessage() {
    console.log(chalk.green('\n🎉 恭喜！所有构建错误已修复'));
    console.log(chalk.blue('\n💡 后续建议:'));
    console.log(chalk.gray('  1. 运行测试确保功能正常'));
    console.log(chalk.gray('  2. 检查修复后的代码是否符合预期'));
    console.log(chalk.gray('  3. 提交代码变更'));
  }

  /**
   * 创建结果对象
   */
  createResult(success, reason = null, remainingErrors = 0) {
    return {
      success,
      attempts: this.buildStats.buildAttempts,
      errorsFixed: this.buildStats.errorsFixed,
      remainingErrors,
      duration: this.buildStats.duration,
      reason
    };
  }

  /**
   * 执行 Dev 模式检测（30秒运行）
   */
  async performDevCheck() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行 dev 命令并在 30 秒后停止
      return await this.executeDevCommand();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 执行构建过程（包含依赖安装和构建）
   */
  async performBuild() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行构建
      return await this.executeBuild();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 安装项目依赖
   */
  async installDependencies() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过依赖安装'));
      return;
    }

    this.updateSpinner('正在安装依赖...');

    try {
      const installOutput = execSync(this.options.installCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 依赖安装完成'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(installOutput));
        this.startSpinner('继续处理...');
      }
    } catch (error) {
      // 尝试使用 legacy peer deps 重新安装
      if (this.options.useLegacyPeerDeps && this.shouldUseLegacyPeerDeps(error)) {
        await this.installWithLegacyPeerDeps();
      } else {
        this.stopSpinner();
        console.log(chalk.yellow('⚠️ 依赖安装可能不完整，继续尝试构建'));
        this.logInstallError(error);
        this.startSpinner('继续处理...');
      }
    }
  }

  /**
   * 检查是否应该使用 legacy peer deps
   */
  shouldUseLegacyPeerDeps(error) {
    const errorOutput = error.stdout + error.stderr;
    return errorOutput.includes('ERESOLVE unable to resolve dependency tree') ||
           errorOutput.includes('Fix the upstream dependency conflict') ||
           errorOutput.includes('--legacy-peer-deps') ||
           errorOutput.includes('peer dependency') ||
           errorOutput.includes('Could not resolve dependency');
  }

  /**
   * 使用 legacy peer deps 安装依赖
   */
  async installWithLegacyPeerDeps() {
    this.updateSpinner('检测到依赖冲突，尝试使用 --legacy-peer-deps 重新安装...');

    try {
      const legacyCommand = this.options.installCommand + ' --legacy-peer-deps';
      const legacyInstallOutput = execSync(legacyCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 使用 --legacy-peer-deps 安装依赖成功'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(legacyInstallOutput));
        this.startSpinner('继续处理...');
      }
    } catch (legacyError) {
      this.stopSpinner();
      console.log(chalk.red('❌ 即使使用 --legacy-peer-deps 也无法安装依赖'));
      this.logInstallError(legacyError);
      this.startSpinner('继续处理...');
    }
  }

  /**
   * 执行 Dev 命令（30秒超时）
   */
  async executeDevCommand() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过 dev 命令执行'));
      return { success: true, output: 'DRY RUN - Dev command skipped' };
    }

    const devCommand = this.options.devCommand || 'pnpm dev';
    const timeout = this.options.devTimeout || 30000; // 30秒

    this.updateSpinner(`执行 dev 命令: ${devCommand} (${timeout / 1000}s 超时)`);
    this.buildStats.buildAttempts++;

    return new Promise((resolve) => {
      const { spawn } = require('child_process');
      let output = '';
      let hasErrors = false;

      const child = spawn('sh', ['-c', devCommand], {
        cwd: this.projectPath,
        stdio: 'pipe'
      });

      // 收集输出
      child.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;

        // 检查是否有错误
        if (this.containsErrors(text)) {
          hasErrors = true;
        }

        if (this.options.verbose) {
          process.stdout.write(chalk.gray(text));
        }
      });

      child.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;
        hasErrors = true;

        if (this.options.verbose) {
          process.stderr.write(chalk.red(text));
        }
      });

      // 30秒后停止进程
      const timer = setTimeout(() => {
        child.kill('SIGTERM');

        // 如果进程没有优雅退出，强制杀死
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);

        this.stopSpinner();

        if (hasErrors) {
          console.log(chalk.red('❌ Dev 模式检测到错误'));
          resolve(this.handleBuildError({ stdout: output, stderr: '' }));
        } else {
          console.log(chalk.green('✅ Dev 模式运行正常'));
          resolve({ success: true, output });
        }
      }, timeout);

      child.on('exit', (code) => {
        clearTimeout(timer);
        this.stopSpinner();

        if (code !== 0 || hasErrors) {
          console.log(chalk.red('❌ Dev 命令执行失败'));
          resolve(this.handleBuildError({ stdout: output, stderr: '' }));
        } else {
          console.log(chalk.green('✅ Dev 命令执行成功'));
          resolve({ success: true, output });
        }
      });
    });
  }

  /**
   * 检查文本中是否包含错误
   */
  containsErrors(text) {
    const errorPatterns = [
      /error/i,
      /ERROR/,
      /failed/i,
      /FAILED/,
      /cannot find module/i,
      /module not found/i,
      /syntax error/i,
      /type error/i,
      /compilation error/i,
      /build error/i
    ];

    return errorPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 执行构建命令
   */
  async executeBuild() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过构建执行'));
      return { success: true, output: 'DRY RUN - Build skipped' };
    }

    this.updateSpinner(`执行构建命令: ${this.options.buildCommand}`);
    this.buildStats.buildAttempts++;

    try {
      const output = execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return {
        success: true,
        output
      };
    } catch (error) {
      this.stopSpinner();
      console.log(chalk.red('❌ 构建失败'));
      const result = this.handleBuildError(error);
      this.startSpinner('继续处理...');
      return result;
    }
  }

  /**
   * 处理构建错误
   */
  handleBuildError(error) {
    const errorOutput = error.stdout + error.stderr;
    const errorSummary = this.extractErrorSummary(errorOutput);

    // 将完整错误日志写入文件
    this.logErrorToFile(errorOutput);

    if (errorSummary.length > 0) {
      console.log(chalk.red('\n构建错误摘要:'));
      // 只显示前20-30条错误给用户
      const displayCount = Math.min(25, errorSummary.length);
      errorSummary.slice(0, displayCount).forEach(line => {
        console.log(chalk.gray(`  ${line.trim()}`));
      });

      if (errorSummary.length > displayCount) {
        console.log(chalk.gray(`  ... 以及 ${errorSummary.length - displayCount} 个其他错误`));
        console.log(chalk.blue(`📄 完整错误日志已保存到: ${this.getErrorLogPath()}`));
      }
    } else {
      console.log(chalk.yellow('⚠️ 无法提取具体错误信息'));
      this.logBasicErrorInfo(errorOutput);
      this.suggestCommonSolutions(errorOutput);
    }

    return {
      success: false,
      output: errorOutput,
      errorSummary: errorSummary.join('\n'),
      error: error.message
    };
  }

  /**
   * 打印构建统计
   */
  printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.buildStats.buildAttempts} 次`);
    console.log(`发现错误: ${this.buildStats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.buildStats.errorsFixed} 个`);
    console.log(`最终状态: ${this.buildStats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);
  }

}

module.exports = BuildFixer;
